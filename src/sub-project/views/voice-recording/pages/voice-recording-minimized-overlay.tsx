/**
 * 语音录制最小化浮窗组件
 * 独立的浮窗组件，使用 abcOverlay.show 显示
 */
import { BaseComponent } from "../../../base-ui/base-component";
import { VoiceRecordingOverlayManager } from "../manage/voice-recording-overlay-manager";
import { renderMinimizedView } from "../renders/minimized-view-renders";
import { TouchPosition } from "../utils/types";
import { sharedPreferences } from "../../../base-business/preferences/shared-preferences";
import { VoiceRecordingOverlayViewLocal, parsePositionData, ensureSafePosition } from "../utils/voice-recording-utils";
import AbcAudioView from "../../../base-ui/views/abc-audio-view";

interface MinimizedOverlayProps {}

interface MinimizedOverlayState {
    position: { top: number; left: number };
    forceUpdate: number; // 用于强制重新渲染
}

export default class VoiceRecordingMinimizedOverlay extends BaseComponent<MinimizedOverlayProps, MinimizedOverlayState> {
    touchPointPosition: TouchPosition = { top: 0, right: 0 };
    private audioViewRef: AbcAudioView | null = null; // 音频播放组件引用

    constructor(props: MinimizedOverlayProps) {
        super(props);

        // 获取保存的位置
        const savedPosition = sharedPreferences.getObject(VoiceRecordingOverlayViewLocal);
        const parsedPosition = parsePositionData(savedPosition);
        const initialPosition = ensureSafePosition(parsedPosition, 80, 80);

        this.state = {
            position: initialPosition,
            forceUpdate: 0,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        console.log("VoiceRecordingMinimizedOverlay componentDidMount");
        VoiceRecordingOverlayManager.instance.setMinimizedOverlayRef(this);
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        // 清理引用
        VoiceRecordingOverlayManager.instance.setMinimizedOverlayRef(null);
    }

    // 触摸事件处理
    private handleTouchDown = (e: any) => {
        this.touchPointPosition = {
            top: e.page_y - this.state.position.top,
            right: e.page_x - this.state.position.left,
        };
    };

    private handleTouchEnd = (e: any) => {
        const newPosition = {
            top: e.page_y - this.touchPointPosition.top,
            left: e.page_x - this.touchPointPosition.right,
        };
        sharedPreferences.setObject(VoiceRecordingOverlayViewLocal, JSON.stringify(newPosition));
        this.setState({ position: newPosition });
    };

    private handleTouchMove = (e: any) => {
        const newPosition = {
            top: e.page_y - this.touchPointPosition.top,
            left: e.page_x - this.touchPointPosition.right,
        };
        this.setState({ position: newPosition });
    };

    private handleTouchCancel = () => {
        sharedPreferences.setObject(VoiceRecordingOverlayViewLocal, JSON.stringify(this.state.position));
    };

    // 切换到全屏模式
    private handleToggleSize = () => {
        VoiceRecordingOverlayManager.instance.hideMinimizedOverlay();
        VoiceRecordingOverlayManager.instance.setState({
            isMinimized: false,
            fullscreenOpacity: 1,
            minimizedOpacity: 0,
        });

        console.log("Switching back to fullscreen - timer should continue running");
        VoiceRecordingOverlayManager.instance.showFullscreenView();
    };

    // 暂停/开始录制 - 使用管理器的公共方法
    private handleStartPauseRecord = async (): Promise<void> => {
        VoiceRecordingOverlayManager.instance.handleStartPauseRecord();
    };

    private prepareRenderProps = () => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        const combinedState = {
            ...managerState,
            position: this.state.position,
        };

        return {
            state: combinedState,
            touchPointPosition: this.touchPointPosition,
            onToggleSize: this.handleToggleSize,
            onPauseRecord: () => {
                this.handleStartPauseRecord();
            },
            onStartRecord: () => {
                this.handleStartPauseRecord();
            },
            onTouchDown: this.handleTouchDown,
            onTouchEnd: this.handleTouchEnd,
            onTouchMove: this.handleTouchMove,
            onTouchCancel: this.handleTouchCancel,
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onDialogConfirm: () => {},
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onDialogCancel: () => {},
            fullscreenOpacityAnimation: null,
            minimizedOpacityAnimation: null,
            setAudioViewRef: (ref: AbcAudioView | null) => {
                this.audioViewRef = ref;
            },
        };
    };

    render() {
        const renderProps = this.prepareRenderProps();
        return renderMinimizedView(renderProps);
    }
}

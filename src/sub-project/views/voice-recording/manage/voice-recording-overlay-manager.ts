/**
 * 语音录制浮窗管理器 - 重构版本
 * 使用模块化设计，将功能拆分到不同的管理器中
 */
import React from "react";
import { callNativeWithPromise } from "@hippy/react";
import type { VoiceRecordingOverlayViewProps } from "../utils/types";
import { getDefaultASRConfig, TAG } from "../utils/voice-recording-utils";
import { VoiceRecordSegment } from "../utils/types";
import { Version } from "../../../base-ui/utils/version-utils";
import { AppInfo } from "../../../base-business/config/app-info";
import { DialogIndex, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { Asr, AsrResult } from "../../../AI/asr";
import { ABCApiNetwork } from "../../../net";
import { MedicalRecord, Patient } from "../../../base-business/data/beans";
import { userCenter } from "../../../user-center";
import { abcNetDelegate } from "../../../net/abc-net-delegate";
import { environment } from "../../../base-business/config/environment";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { LoadingDialog } from "../../../base-ui/dialog/loading-dialog";

// 导入拆分后的模块
import { StateManager } from "./state-manager";
import { AudioManager } from "./audio-manager";
import { WaveformManager } from "./waveform-manager";
import { RecordingController } from "./recording-controller";
import { ASREventHandler } from "./asr-event-handler";
import { VoiceSynthesizer } from "./voice-synthesizer";
import { NavigationManager } from "./navigation-manager";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";
import { DATA_EVENTS } from "../utils/constants";
import { OutpatientInvoiceDetail } from "../../../outpatient/data/outpatient-beans";
import { upgradeDataManager } from "../../../upgrade/upgrade";

export interface MRKey {
    key: string;
    label: string;
    matchLabel: string;
}

// 重构后的全局管理类
export class VoiceRecordingOverlayManager {
    public static instance: VoiceRecordingOverlayManager;

    // 业务相关属性
    public currentProps: VoiceRecordingOverlayViewProps = {};
    public medicalRecordData: any = null;
    public draft: OutpatientInvoiceDetail = {} as OutpatientInvoiceDetail;
    private loadingDialog = new LoadingDialog("正在识别");

    // 模块化管理器
    private stateManager: StateManager;
    private audioManager: AudioManager;
    private waveformManager: WaveformManager;
    private recordingController: RecordingController;
    private asrEventHandler: ASREventHandler;
    private voiceSynthesizer: VoiceSynthesizer;
    private navigationManager: NavigationManager;

    constructor() {
        // 初始化所有管理器
        this.stateManager = new StateManager();
        this.audioManager = new AudioManager(this.stateManager);
        this.waveformManager = new WaveformManager(this.stateManager);
        this.recordingController = new RecordingController(this.stateManager, this.audioManager, this.waveformManager);
        this.asrEventHandler = new ASREventHandler(this.stateManager, this.waveformManager);
        this.asrEventHandler.setManagerRef(this); // 设置管理器引用
        this.voiceSynthesizer = new VoiceSynthesizer(this.waveformManager);
        this.navigationManager = new NavigationManager(this.stateManager);
        this.navigationManager.setManagerRef(this); // 设置管理器引用
    }

    static getInstance(): VoiceRecordingOverlayManager {
        if (!VoiceRecordingOverlayManager.instance) {
            VoiceRecordingOverlayManager.instance = new VoiceRecordingOverlayManager();
        }
        return VoiceRecordingOverlayManager.instance;
    }

    // ================= 状态管理方法 - 委托给StateManager =================
    public getState() {
        return this.stateManager.getState();
    }

    public setState(newState: Partial<any>, callback?: () => void) {
        this.stateManager.setState(newState, callback);
    }

    // ================= 录制控制方法 - 统一管理，消除重复逻辑 =================
    public async handleStartPauseRecord(): Promise<void> {
        await this.recordingController.handleStartPauseRecord();
    }

    public handleStartRecord = async (): Promise<void> => {
        await this.recordingController.handleStartRecord();
    };

    /**
     * 停止录制 - 统一入口
     * @param playSound 是否播放停止音效
     * @param hideOverlay 是否隐藏浮窗
     */
    public async handleStopRecord(showDialog = false): Promise<void> {
        // 执行核心停止录制逻辑
        await this.recordingController.handleStopRecord();
        this.hide();

        await delayed(100).toPromise();

        const state = this.stateManager.getState();
        if (state.enableMedicalRecordTranscription) {
            if (showDialog) {
                this.loadingDialog = new LoadingDialog("正在识别");
                this.loadingDialog.show();
            }
        } else {
            if (showDialog) {
                await this.navigateDialog({
                    title: "",
                    content: "录音完成，请在电脑端查看病历",
                    icon: "s-singlecheck-line",
                });
                await delayed(2000).toPromise();
                ABCNavigator.pop();
            }
        }
    }

    // ================= 引用管理方法 =================
    public setAsrRecordingRef(ref: any): void {
        this.stateManager.setAsrRecordingRef(ref);
    }

    public setAsrOverlayRecordingRef(ref: any): void {
        this.navigationManager.setRecordRef(ref);
        this.stateManager.setAsrRecordRef(ref);
    }

    public setMinimizedOverlayRef(ref: any): void {
        this.stateManager.setMinimizedOverlayRef(ref);
    }

    // ================= 导航管理方法 - 委托给NavigationManager =================
    public showMinimizedOverlay(): void {
        this.navigationManager.showMinimizedOverlay();
    }

    public hideMinimizedOverlay(): void {
        this.navigationManager.hideMinimizedOverlay();
    }

    public showFullscreenView(replace = false): void {
        this.navigationManager.showFullscreenView(replace);
    }

    public async navigateMedicalRecordPage(): Promise<void> {
        await this.navigationManager.navigateMedicalRecordPage();
    }

    public async navigateDialog(props: { title: string; content: string; icon?: string }): Promise<void> {
        await this.navigationManager.navigateDialog(props);
    }

    public handleBackToRecord = (): void => {
        this.navigationManager.handleBackToRecord();
    };

    // ================= 语音合成方法 - 委托给VoiceSynthesizer =================
    public async synthesizeVoiceRecording(voiceUrls: string[]): Promise<string> {
        return await this.voiceSynthesizer.synthesizeVoiceRecording(voiceUrls);
    }

    // ================= 播放控制方法 - 简化版本，只保留必要的引用管理 =================
    private voiceViewRef: any = null;

    public setVoiceViewRef(ref: any): void {
        this.voiceViewRef = ref;
    }

    public getVoiceViewRef(): any {
        return this.voiceViewRef;
    }

    // ================= 病历解析方法 =================
    public parseMedicalRecord = (medicalRecord: string | null, mrKeys: MRKey[]): any => {
        const result: MedicalRecord = new MedicalRecord();
        if (!medicalRecord) {
            return result;
        }
        const trimedMedicalRecord = medicalRecord.split("**").join("");
        mrKeys.forEach((item) => {
            const { key, matchLabel } = item;
            const regex = new RegExp(`${matchLabel}[：:](.*?)(?=\\n\\n|\\n[^\\n]|$)`, "s");
            const match = trimedMedicalRecord?.match(regex);

            if (match && match[1]?.trim()) {
                (result as any)[key] = match[1].trim();
            }
        });
        console.log("解析后的病历数据:", { ...result });
        this.medicalRecordData = result;
        return result;
    };

    // ================= 启动和停止方法 =================
    run(VoiceRecordingOverlayViewComponent?: React.ComponentType<any>): void {
        console.log(TAG, "VoiceRecordingOverlayManager.run - initializing manager");

        // 保存组件类引用，供show方法使用
        if (VoiceRecordingOverlayViewComponent) {
            this.navigationManager.setVoiceRecordingOverlayViewComponent(VoiceRecordingOverlayViewComponent);
        } else {
            console.warn(TAG, "VoiceRecordingOverlayViewComponent not provided to run method");
        }

        // 初始化ASR配置
        const appToken = userCenter.appToken();
        const grayFlag = abcNetDelegate.grayFlag;
        const socketUrl = `${environment.serverHostScheme}://${environment.serverHostName}`;
        const extraHeaders: any = {
            grayflag: grayFlag ?? "",
            APP_UA: environment.appUA() || "",
            Origin: environment.serverHostScheme + "://" + environment.serverHostName,
            "Content-Type": "application/json",
            Authorization: `Bearer ${appToken}`,
        };
        const _params = {
            namespace: `/cis${DeviceUtils.isAndroid() ? "?supplierId=tencent" : ""}`,
            cookies: `_abcyun_token_=${appToken}; domain=${environment.serverHostName}; path=/`,
            extraHeaders: extraHeaders,
            connectParams: DeviceUtils.isIOS() || DeviceUtils.isOhos() ? { supplierId: "tencent" } : undefined,
            log: true,
            forceWebsockets: true,
        };
        const config = getDefaultASRConfig(socketUrl, _params);
        callNativeWithPromise("AbcASR", "init", config).then(() => {
            this.asrEventHandler.registerSocketEventListeners();
            this.asrEventHandler.registerASREventListeners();
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: DATA_EVENTS.APP_READY,
                data: {},
            });
        });

        console.log(TAG, "VoiceRecordingOverlayManager initialized successfully");
    }

    stop(): void {
        console.log(TAG, "VoiceRecordingOverlayManager.stop - stopping manager");

        this.handleStopRecord().then(() => {
            // 清理各个管理器
            this.waveformManager.cleanup();
            this.asrEventHandler.cleanup();
            this.recordingController.cleanup();
            this.navigationManager.cleanup();

            // 清理播放相关引用
            this.voiceViewRef = null;

            // 释放ASR资源
            callNativeWithPromise("AbcASR", "release", {});

            // 重置业务状态
            this.currentProps = {};
            this.medicalRecordData = null;
            this.draft = {} as OutpatientInvoiceDetail;

            // 重置状态管理器
            this.stateManager.resetState();

            console.log(TAG, "VoiceRecordingOverlayManager stopped successfully");
        });
    }

    // ================= 显示方法 =================
    async show(
        isReplace?: boolean,
        enableMedicalRecordTranscription?: boolean,
        voiceRecordResult?: AsrResult,
        businessId?: string,
        patient?: Patient,
        chineseExamination?: number,
        physicalExamination?: number,
        medicalRecordType?: number,
        draft?: OutpatientInvoiceDetail
    ): Promise<boolean> {
        console.log(TAG, "VoiceRecordingOverlayManager.show - starting voice recording overlay page");

        // 判断版本，低于版本给出升级提示
        const greaterThan286 = new Version(AppInfo.appVersion).compareTo(new Version("2.8.6.0000")) >= 0;
        if (!greaterThan286) {
            const select = await showQueryDialog(
                "更新提示",
                "当前版本不支持语音病历，请更新后体验新功能",
                "立即更新",
                "取消",
                undefined,
                "rgba(0,0,0,0.8)"
            );
            if (select == DialogIndex.positive) {
                const upgradeInfo = await upgradeDataManager.getUpgradeInfo();
                upgradeInfo && (await upgradeDataManager.doUpgrade(upgradeInfo));
            }
            return false;
        }

        // 正在录制，不允许打开
        if (this.getState().isRecording) {
            await this.navigateDialog({
                title: "提示",
                content: "正在录音中，请先停止录音",
            });
            delayed(1000)
                .toPromise()
                .then(() => {
                    ABCNavigator.pop();
                });
            return false;
        }

        let voiceRecordDetail = null;
        // 请求详情接口
        if (voiceRecordResult && voiceRecordResult.id) {
            voiceRecordDetail = await Asr.getAsrResultById(voiceRecordResult.id);
            console.log("语音识别详情:", voiceRecordDetail);
            if (voiceRecordDetail?.result) {
                voiceRecordDetail?.result.forEach((item: VoiceRecordSegment, index: number) => {
                    item.id = "segment_" + index;
                });
            }
        }
        const showMedicalRecordView = Object.keys(voiceRecordDetail ?? {}).length > 0;

        // 更新业务相关状态
        const currentState = this.getState();
        this.setState({
            businessId: businessId ?? currentState.businessId,
            chineseExamination: chineseExamination ?? currentState.chineseExamination,
            physicalExamination: physicalExamination ?? currentState.physicalExamination,
            medicalRecordType: medicalRecordType ?? currentState.medicalRecordType,
        });
        this.draft = draft || ({} as OutpatientInvoiceDetail);
        // 根据条件判断打开哪个页面
        if (showMedicalRecordView) {
            // 如果有语音识别结果，直接打开病历转写页面
            await this.navigateMedicalRecordPage();

            // 等待页面加载完成后设置状态
            await new Promise((resolve) => setTimeout(resolve, 200));

            // 更新管理器状态，页面会自动同步
            this.setState({
                // 回显相关
                voiceRecordSegments: voiceRecordDetail?.result ?? [], // 语音识别结果
                voiceRecordResult: voiceRecordDetail ?? null, // 语音识别结果
                showMedicalRecordView: showMedicalRecordView, // 是否显示病历
                medicalRecordData: voiceRecordDetail?.aiResult ?? "未识别到有效病历内容", // 病历数据
                created: voiceRecordDetail?.created ?? "",
                totalDuration: voiceRecordDetail?.duration ?? 0,
                isTranscribing: false,
                patient: patient,
                currentTab: "medical",
                currentPlayTime: 0,
                isPlaying: false,
            });

            // 同时更新管理器状态
            this.setState({
                showMedicalRecordView: showMedicalRecordView,
            });
        } else {
            console.log(TAG, "[ShowDebug] No voice record result, showing recording view");

            // 更新管理器状态
            this.setState({
                showRecord: true,
                enableMedicalRecordTranscription: enableMedicalRecordTranscription ?? false,
                isMinimized: false,
                fullscreenOpacity: 1,
                minimizedOpacity: 0,
                businessId: businessId ?? currentState.businessId,
                chineseExamination: chineseExamination ?? 0,
                physicalExamination: physicalExamination ?? 0,
                medicalRecordType: medicalRecordType ?? 0,
                duration: 0,
            });

            console.log(TAG, "[ShowDebug] State updated, calling showFullscreenView");

            // 显示全屏视图
            this.showFullscreenView(isReplace);
            await new Promise((resolve) => setTimeout(resolve, 200));
        }
        console.log(TAG, "VoiceRecordingOverlayManager.show - overlay page displayed successfully");
        return true;
    }

    public hide(): void {
        this.waveformManager.pauseAnimation();
        this.waveformManager.resetWaveformFrequencyControl();
        this.navigationManager.hide();
    }

    // ================= ASR处理方法 =================
    private async asrParseSSE(resultText: string, chineseExamination: number, physicalExamination: number, medicalRecordType: number) {
        let medicalRecordDataContent = "";
        const res = await ABCApiNetwork.connectSSE("ai/analysis/text", {
            method: "POST",
            body: {
                text: resultText,
                data: {
                    chineseExamination: {
                        value: chineseExamination,
                    },
                    physicalExamination: {
                        value: physicalExamination,
                    },
                    medicalRecordType: {
                        value: medicalRecordType,
                    },
                },
                promptName: "asr-generate-medical-record",
            },
            listeners: {
                onopen: (event) => {
                    console.log("连接已打开:", event.connectionId);
                },
                onmessage: (event) => {
                    const data = JSON.parse(event.data);
                    if (!data || data.code !== 200) {
                        console.error("接口错误:", data);
                        return;
                    }

                    // 处理回答内容
                    if (data.data.answer) {
                        medicalRecordDataContent += data.data.answer;
                        // 更新管理器状态，页面会自动同步
                        this.setState({
                            medicalRecordData: medicalRecordDataContent,
                        });
                    }
                    console.log("收到消息:", data);
                },
                onerror: (event) => {
                    console.error("连接错误:", event.error);
                },
            },
        });
        return { medicalRecordDataContent, res };
    }

    public async handleASRCompleted(): Promise<void> {
        await this.loadingDialog.hide();
        // 显示病历转写视图并开始转写
        this.setState({
            showMedicalRecordView: true,
            isMinimized: false,
        });

        // 使用 manager 中的 navigateMedicalRecordPage 方法
        await this.navigateMedicalRecordPage();
        let medicalRecordDataContent = "";
        let resultText = "";
        const currentState = this.getState();
        for (let i = 0; i < currentState.recognitionResults.length; i++) {
            resultText += currentState.recognitionResults[i].text;
        }
        Asr.getAsrResultByTaskId(currentState.taskId, currentState.businessId).then((voiceRecordDetail) => {
            this.asrParseSSE(
                resultText,
                currentState.chineseExamination,
                currentState.physicalExamination,
                currentState.medicalRecordType
            ).then((__ret: any) => {
                medicalRecordDataContent = __ret.medicalRecordDataContent;
                const res = __ret.res;
                console.log(
                    "初始文本-> ",
                    resultText,
                    "全部结束->",
                    medicalRecordDataContent,
                    "res ->",
                    res,
                    "messageObj->" + JSON.stringify(voiceRecordDetail)
                );
                if (voiceRecordDetail) {
                    Asr.saveAsrResult(voiceRecordDetail.id, medicalRecordDataContent);
                }
                if (voiceRecordDetail && voiceRecordDetail.result) {
                    voiceRecordDetail.result.forEach((item: VoiceRecordSegment, index: number) => {
                        item.id = "segment_" + index;
                    });
                }
                // 更新管理器状态，页面会自动同步
                this.setState({
                    isTranscribing: false,
                    voiceRecordResult: voiceRecordDetail,
                    voiceRecordSegments: voiceRecordDetail?.result || [],
                    totalDuration: voiceRecordDetail?.duration || 0,
                });
            });
        });
        return;
    }
}

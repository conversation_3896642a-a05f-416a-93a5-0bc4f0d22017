/**
 * 语音合成器
 * 负责处理语音文件的下载、合成等操作
 */

import { FileDownloader } from "../../../base-business/file/file-downloader";
import FileUtils from "../../../common-base-module/file/file-utils";
import { UUIDGen } from "../../../common-base-module/utils";
import { WaveformManager } from "./waveform-manager";

const TAG = "VoiceSynthesizer";

/**
 * 音频格式信息接口
 */
interface AudioFormat {
    sampleRate: number; // 采样率
    bitsPerSample: number; // 位深度
    channels: number; // 声道数
    byteRate: number; // 字节率
    blockAlign: number; // 块对齐
    dataSize: number; // 数据大小
    dataOffset: number; // 数据块在文件中的偏移位置
    headerSize: number; // 文件头大小
}

/**
 * 音频片段信息接口
 */
interface AudioSegment {
    data: Uint8Array; // 纯音频数据（不包含头）
    format: AudioFormat; // 音频格式信息
}

export class VoiceSynthesizer {
    private waveformManager: WaveformManager;

    constructor(waveformManager: WaveformManager) {
        this.waveformManager = waveformManager;
    }

    /**
     * 语音合成控制逻辑方法
     * 下载多段音频文件，读取二进制数据，合成为一段音频，然后写回终端
     * @param voiceUrls 音频URL数组
     * @returns Promise<string> 合成后的音频文件路径
     */
    public async synthesizeVoiceRecording(voiceUrls: string[]): Promise<string> {
        console.log(TAG, "synthesizeVoiceRecording start, voiceUrls:", voiceUrls);

        if (!voiceUrls || voiceUrls.length === 0) {
            throw new Error("语音URL数组不能为空");
        }

        try {
            // 1. 下载所有音频文件
            const downloadedFiles = await this.downloadVoiceFiles(voiceUrls);
            console.log(TAG, "downloadedFiles:", downloadedFiles);

            // 2. 读取所有音频文件的二进制数据
            const audioDataList = await this.readVoiceFiles(downloadedFiles);
            console.log(TAG, "audioDataList length:", audioDataList.length);

            // 3. 合成音频数据
            const synthesizedAudioData = await this.mergeAudioData(audioDataList);
            console.log(TAG, "synthesizedAudioData length:", synthesizedAudioData.length);

            // 4. 写入合成后的音频文件
            const outputFilePath = await this.writeSynthesizedAudio(synthesizedAudioData);
            console.log(TAG, "synthesizeVoiceRecording completed, outputFilePath:", outputFilePath);

            // 5. 清理临时文件
            await this.cleanupTempFiles(downloadedFiles);

            return outputFilePath;
        } catch (error) {
            console.error(TAG, "synthesizeVoiceRecording error:", error);
            throw error;
        }
    }

    /**
     * 下载语音文件
     * @param voiceUrls 音频URL数组
     * @returns Promise<string[]> 下载后的文件路径数组
     */
    private async downloadVoiceFiles(voiceUrls: string[]): Promise<string[]> {
        const downloadedFiles: string[] = [];
        const tmpDir = await FileUtils.getTmpDir();

        for (let i = 0; i < voiceUrls.length; i++) {
            const url = voiceUrls[i];
            const fileName = `voice_segment_${i}_${UUIDGen.generate()}.wav`;
            const filePath = `${tmpDir}/${fileName}`;

            try {
                console.log(TAG, `Downloading voice file ${i + 1}/${voiceUrls.length}: ${url}`);

                // 使用FileDownloader.downloadFile方法下载文件
                const response = await FileDownloader.downloadFile(url, {
                    filePath: filePath,
                    method: "GET",
                    redirect: "follow",
                    onProgress: (currentBytes, totalBytes) => {
                        const progress = Math.round((currentBytes / totalBytes) * 100);
                        console.log(TAG, `Download progress for file ${i + 1}: ${progress}%`);
                    },
                });

                console.log(TAG, `Download response:`, response);
                if (response && response.filePath) {
                    downloadedFiles.push(response.filePath);
                } else {
                    throw new Error(`下载文件失败: ${url}`);
                }
            } catch (error) {
                console.error(TAG, `下载文件失败 ${url}:`, error);
                throw error;
            }
        }

        return downloadedFiles;
    }

    /**
     * 读取语音文件的二进制数据
     * @param filePaths 文件路径数组
     * @returns Promise<string[]> 音频数据数组（base64编码）
     */
    private async readVoiceFiles(filePaths: string[]): Promise<string[]> {
        const audioDataList: string[] = [];

        for (const filePath of filePaths) {
            try {
                console.log(TAG, `Reading voice file: ${filePath}`);

                const audioData: string = await FileUtils.readAsString(filePath, "base64");

                if (audioData) {
                    audioDataList.push(audioData);
                } else {
                    throw new Error(`读取文件失败: ${filePath}`);
                }
            } catch (error) {
                console.error(TAG, `读取文件失败 ${filePath}:`, error);
                throw error;
            }
        }

        return audioDataList;
    }

    /**
     * 合成音频数据
     * @param audioDataList 音频数据数组（base64编码）
     * @returns Promise<string> 合成后的音频数据（base64编码）
     */
    private async mergeAudioData(audioDataList: string[]): Promise<string> {
        console.log(TAG, "mergeAudioData start, audioDataList length:", audioDataList.length);

        if (audioDataList.length === 0) {
            throw new Error("音频数据数组不能为空");
        }

        try {
            // 将base64数据转换为字节数组并验证格式
            const audioByteArrays: Uint8Array[] = [];
            const audioFormats: AudioFormat[] = [];

            for (let i = 0; i < audioDataList.length; i++) {
                const base64Data = audioDataList[i];
                const bytes = this.base64ToUint8Array(base64Data);

                // 验证WAV文件格式
                const format = this.validateAndParseWavFormat(bytes, i);
                if (!format) {
                    throw new Error(`音频文件 ${i} 格式无效或不支持`);
                }

                audioByteArrays.push(bytes);
                audioFormats.push(format);
            }

            // 验证所有音频文件格式一致性
            this.validateAudioFormatConsistency(audioFormats);

            // 调用优化后的WAV文件合成方法
            const mergedBytes = this.concatenateWavFilesOptimized(audioByteArrays, audioFormats);
            if (!mergedBytes) {
                throw new Error("WAV文件合成失败");
            }

            // 合成后的音频计算波形数据，调用this.updateWaveformData()保存
            const waveformData = this.analyzeWaveform(mergedBytes);
            this.waveformManager.updateWaveformData(waveformData);

            // 将合成后的字节数组转换回base64
            const mergedBase64 = this.uint8ArrayToBase64(mergedBytes);

            console.log(TAG, "Audio data merged successfully");
            return mergedBase64;
        } catch (error) {
            console.error(TAG, "合成音频数据失败:", error);
            throw error;
        }
    }

    /**
     * 写入合成后的音频文件
     * @param synthesizedAudioData 合成后的音频数据（base64编码）
     * @returns Promise<string> 输出文件路径
     */
    private async writeSynthesizedAudio(synthesizedAudioData: string): Promise<string> {
        try {
            const tmpDir = await FileUtils.getTmpDir();
            const outputFileName = `synthesized_voice_${UUIDGen.generate()}.wav`;
            const outputFilePath = `${tmpDir}/${outputFileName}`;

            console.log(TAG, `Writing synthesized audio to: ${outputFilePath}`);

            await FileUtils.writeAsString(outputFilePath, synthesizedAudioData, "base64");

            console.log(TAG, "Synthesized audio written successfully");
            return outputFilePath;
        } catch (error) {
            console.error(TAG, "writeSynthesizedAudio error:", error);
            throw error;
        }
    }

    /**
     * 清理临时文件
     * @param filePaths 要清理的文件路径数组
     */
    private async cleanupTempFiles(filePaths: string[]): Promise<void> {
        console.log(TAG, "Cleaning up temporary files...");

        for (const filePath of filePaths) {
            try {
                await FileUtils.deleteFile(filePath);
                console.log(TAG, `Deleted temporary file: ${filePath}`);
            } catch (error) {
                console.warn(TAG, `Failed to delete temporary file ${filePath}:`, error);
                // 继续清理其他文件，不抛出错误
            }
        }

        console.log(TAG, "Temporary files cleanup completed");
    }

    /**
     * 验证并解析WAV文件格式
     * @param audioData 音频字节数组
     * @param fileIndex 文件索引（用于错误提示）
     * @returns AudioFormat | null 音频格式信息
     */
    private validateAndParseWavFormat(audioData: Uint8Array, fileIndex: number): AudioFormat | null {
        if (audioData.length < 12) {
            console.error(TAG, `文件 ${fileIndex} 太小，不是有效的WAV文件`);
            return null;
        }

        try {
            // 验证RIFF头
            const riffHeader = String.fromCharCode(...audioData.slice(0, 4));
            if (riffHeader !== "RIFF") {
                console.error(TAG, `文件 ${fileIndex} 不是有效的RIFF文件`);
                return null;
            }

            // 验证WAV格式
            const waveHeader = String.fromCharCode(...audioData.slice(8, 12));
            if (waveHeader !== "WAVE") {
                console.error(TAG, `文件 ${fileIndex} 不是有效的WAV文件`);
                return null;
            }

            // 查找fmt块
            let offset = 12;
            let fmtFound = false;
            let channels = 0,
                sampleRate = 0,
                byteRate = 0,
                blockAlign = 0,
                bitsPerSample = 0;

            while (offset < audioData.length - 8) {
                const chunkId = String.fromCharCode(...audioData.slice(offset, offset + 4));
                const chunkSize =
                    audioData[offset + 4] | (audioData[offset + 5] << 8) | (audioData[offset + 6] << 16) | (audioData[offset + 7] << 24);

                if (chunkId === "fmt ") {
                    if (chunkSize < 16) {
                        console.error(TAG, `文件 ${fileIndex} fmt块大小无效`);
                        return null;
                    }

                    // 解析fmt块
                    const audioFormat = audioData[offset + 8] | (audioData[offset + 9] << 8);
                    if (audioFormat !== 1) {
                        // 只支持PCM格式
                        console.error(TAG, `文件 ${fileIndex} 不支持的音频格式: ${audioFormat}`);
                        return null;
                    }

                    channels = audioData[offset + 10] | (audioData[offset + 11] << 8);
                    sampleRate =
                        audioData[offset + 12] |
                        (audioData[offset + 13] << 8) |
                        (audioData[offset + 14] << 16) |
                        (audioData[offset + 15] << 24);
                    byteRate =
                        audioData[offset + 16] |
                        (audioData[offset + 17] << 8) |
                        (audioData[offset + 18] << 16) |
                        (audioData[offset + 19] << 24);
                    blockAlign = audioData[offset + 20] | (audioData[offset + 21] << 8);
                    bitsPerSample = audioData[offset + 22] | (audioData[offset + 23] << 8);

                    fmtFound = true;
                    break;
                }

                offset += 8 + chunkSize;
                // 确保块大小是偶数（WAV规范要求）
                if (chunkSize % 2 === 1) offset++;
            }

            if (!fmtFound) {
                console.error(TAG, `文件 ${fileIndex} 未找到fmt块`);
                return null;
            }

            // 查找data块
            offset = 12;
            let dataOffset = 0;
            let dataSize = 0;
            let dataFound = false;

            while (offset < audioData.length - 8) {
                const chunkId = String.fromCharCode(...audioData.slice(offset, offset + 4));
                const chunkSize =
                    audioData[offset + 4] | (audioData[offset + 5] << 8) | (audioData[offset + 6] << 16) | (audioData[offset + 7] << 24);

                if (chunkId === "data") {
                    dataOffset = offset + 8;
                    dataSize = chunkSize;
                    dataFound = true;
                    break;
                }

                offset += 8 + chunkSize;
                if (chunkSize % 2 === 1) offset++;
            }

            if (!dataFound) {
                console.error(TAG, `文件 ${fileIndex} 未找到data块`);
                return null;
            }

            const format: AudioFormat = {
                sampleRate,
                bitsPerSample,
                channels,
                byteRate,
                blockAlign,
                dataSize,
                dataOffset,
                headerSize: dataOffset,
            };

            console.log(TAG, `文件 ${fileIndex} 格式:`, format);
            return format;
        } catch (error) {
            console.error(TAG, `解析文件 ${fileIndex} 格式失败:`, error);
            return null;
        }
    }

    /**
     * 验证音频格式一致性
     * @param formats 音频格式数组
     */
    private validateAudioFormatConsistency(formats: AudioFormat[]): void {
        if (formats.length === 0) {
            throw new Error("音频格式数组为空");
        }

        const baseFormat = formats[0];
        for (let i = 1; i < formats.length; i++) {
            const currentFormat = formats[i];

            if (currentFormat.sampleRate !== baseFormat.sampleRate) {
                throw new Error(`音频文件 ${i} 采样率不匹配: ${currentFormat.sampleRate} vs ${baseFormat.sampleRate}`);
            }

            if (currentFormat.bitsPerSample !== baseFormat.bitsPerSample) {
                throw new Error(`音频文件 ${i} 位深度不匹配: ${currentFormat.bitsPerSample} vs ${baseFormat.bitsPerSample}`);
            }

            if (currentFormat.channels !== baseFormat.channels) {
                throw new Error(`音频文件 ${i} 声道数不匹配: ${currentFormat.channels} vs ${baseFormat.channels}`);
            }
        }

        console.log(TAG, "所有音频文件格式一致性验证通过");
    }

    /**
     * 合成WAV文件
     * 参考Java版本的concatenateWavFiles方法实现
     * @param audioDataList 音频字节数组列表
     * @returns Uint8Array | null 合成后的音频数据
     */
    private concatenateWavFiles(audioDataList: Uint8Array[]): Uint8Array | null {
        if (!audioDataList || audioDataList.length === 0) {
            return null;
        }

        try {
            // WAV文件结构：
            // 前44字节是WAV头，包含文件大小、格式等信息
            // 44字节之后是实际音频数据

            // 获取第一个文件的头信息
            const firstFile = audioDataList[0];
            if (firstFile.length < 44) {
                return null; // 文件太小，不是有效的WAV
            }

            // 计算所有文件的音频数据总长度
            let totalAudioDataSize = 0;
            for (let i = 0; i < audioDataList.length; i++) {
                const file = audioDataList[i];
                if (file.length < 44) continue; // 跳过无效文件

                // 从每个WAV文件中提取数据大小（不包括头）
                totalAudioDataSize += file.length - 44;
            }

            // 创建新的WAV文件（头 + 所有数据）
            const result = new Uint8Array(44 + totalAudioDataSize);

            // 复制第一个文件的头信息
            result.set(firstFile.slice(0, 44), 0);

            // 更新WAV头中的文件大小信息
            // 总文件大小 = 总数据大小 + 36 (RIFF块中的大小)
            const totalSize = totalAudioDataSize + 36;
            result[4] = totalSize & 0xff;
            result[5] = (totalSize >> 8) & 0xff;
            result[6] = (totalSize >> 16) & 0xff;
            result[7] = (totalSize >> 24) & 0xff;

            // 更新数据块大小
            result[40] = totalAudioDataSize & 0xff;
            result[41] = (totalAudioDataSize >> 8) & 0xff;
            result[42] = (totalAudioDataSize >> 16) & 0xff;
            result[43] = (totalAudioDataSize >> 24) & 0xff;

            // 复制所有文件的音频数据（跳过每个文件的头）
            let offset = 44; // 从头之后开始
            for (const file of audioDataList) {
                if (file.length < 44) continue; // 跳过无效文件

                // 复制当前文件的音频数据（跳过头）
                result.set(file.slice(44), offset);
                offset += file.length - 44;
            }

            return result;
        } catch (error) {
            console.error(TAG, "concatenateWavFiles error:", error);
            return null;
        }
    }

    /**
     * 优化的WAV文件合成方法
     * 添加音频平滑处理，减少拼接处的突变
     * @param audioDataList 音频字节数组列表
     * @param formats 音频格式信息数组
     * @returns Uint8Array | null 合成后的音频数据
     */
    private concatenateWavFilesOptimized(audioDataList: Uint8Array[], formats: AudioFormat[]): Uint8Array | null {
        if (!audioDataList || audioDataList.length === 0 || !formats || formats.length === 0) {
            return null;
        }

        try {
            // 提取所有音频片段的纯数据
            const audioSegments: AudioSegment[] = [];
            let totalAudioDataSize = 0;

            for (let i = 0; i < audioDataList.length; i++) {
                const file = audioDataList[i];
                const format = formats[i];

                if (file.length < format.headerSize) {
                    console.warn(TAG, `跳过无效文件 ${i}: 文件太小`);
                    continue;
                }

                // 提取纯音频数据（使用正确的偏移位置）
                const audioData = file.slice(format.dataOffset, format.dataOffset + format.dataSize);

                // 确保数据长度是blockAlign的倍数
                const alignedLength = Math.floor(audioData.length / format.blockAlign) * format.blockAlign;
                const alignedData = audioData.slice(0, alignedLength);

                audioSegments.push({
                    data: alignedData,
                    format: format,
                });

                totalAudioDataSize += alignedData.length;
            }

            if (audioSegments.length === 0) {
                throw new Error("没有有效的音频数据");
            }

            // 使用第一个文件的格式作为输出格式
            const outputFormat = formats[0];

            // 创建新的WAV文件
            const headerSize = outputFormat.headerSize;
            const result = new Uint8Array(headerSize + totalAudioDataSize);

            // 复制并更新WAV头
            const firstFile = audioDataList[0];
            result.set(firstFile.slice(0, headerSize), 0);
            this.updateWavHeader(result, totalAudioDataSize, outputFormat);

            // 合成音频数据，添加平滑处理
            let offset = headerSize;
            for (let i = 0; i < audioSegments.length; i++) {
                const segment = audioSegments[i];

                if (i === 0) {
                    // 第一个片段直接复制
                    result.set(segment.data, offset);
                } else {
                    // 后续片段添加交叉淡化处理
                    this.applyCrossfadeOptimized(result, segment, offset, outputFormat);
                }

                offset += segment.data.length;
            }

            console.log(TAG, `音频合成完成，总大小: ${totalAudioDataSize} 字节`);
            return result;
        } catch (error) {
            console.error(TAG, "concatenateWavFilesOptimized error:", error);
            return null;
        }
    }

    /**
     * 更新WAV文件头信息
     * @param result 结果数组
     * @param totalAudioDataSize 总音频数据大小
     * @param format 音频格式信息
     */
    private updateWavHeader(result: Uint8Array, totalAudioDataSize: number, format: AudioFormat): void {
        // 更新RIFF块大小 (总文件大小 - 8)
        const riffSize = totalAudioDataSize + format.headerSize - 8;
        result[4] = riffSize & 0xff;
        result[5] = (riffSize >> 8) & 0xff;
        result[6] = (riffSize >> 16) & 0xff;
        result[7] = (riffSize >> 24) & 0xff;

        // 查找并更新data块大小
        let offset = 12;
        while (offset < result.length - 8) {
            const chunkId = String.fromCharCode(...result.slice(offset, offset + 4));
            if (chunkId === "data") {
                result[offset + 4] = totalAudioDataSize & 0xff;
                result[offset + 5] = (totalAudioDataSize >> 8) & 0xff;
                result[offset + 6] = (totalAudioDataSize >> 16) & 0xff;
                result[offset + 7] = (totalAudioDataSize >> 24) & 0xff;
                break;
            }
            const chunkSize = result[offset + 4] | (result[offset + 5] << 8) | (result[offset + 6] << 16) | (result[offset + 7] << 24);
            offset += 8 + chunkSize;
            if (chunkSize % 2 === 1) offset++;
        }
    }

    /**
     * 优化的交叉淡化处理，正确处理音频采样点
     * @param result 结果数组
     * @param segment 当前音频片段
     * @param offset 偏移位置
     * @param format 音频格式
     */
    private applyCrossfadeOptimized(result: Uint8Array, segment: AudioSegment, offset: number, format: AudioFormat): void {
        const segmentData = segment.data;

        // 计算交叉淡化的采样点数量（5ms，减少处理时间）
        const crossfadeSamples = Math.min(
            Math.floor(format.sampleRate * 0.005), // 5ms的交叉淡化
            Math.floor(segmentData.length / format.blockAlign / 4), // 不超过片段长度的1/4
            Math.floor((offset - format.headerSize) / format.blockAlign / 4) // 不超过前一片段长度的1/4
        );

        const crossfadeBytes = crossfadeSamples * format.blockAlign;

        if (crossfadeBytes <= 0 || crossfadeBytes >= segmentData.length) {
            // 如果无法进行交叉淡化，直接复制
            result.set(segmentData, offset);
            return;
        }

        // 复制非交叉淡化部分
        result.set(segmentData.slice(crossfadeBytes), offset + crossfadeBytes);

        // 应用交叉淡化到重叠区域 - 在采样点级别处理
        if (format.bitsPerSample === 16) {
            // 16位音频处理
            this.applyCrossfade16Bit(result, segmentData, offset, crossfadeSamples, format);
        } else if (format.bitsPerSample === 8) {
            // 8位音频处理
            this.applyCrossfade8Bit(result, segmentData, offset, crossfadeBytes);
        } else {
            // 不支持的位深度，直接复制
            console.warn(TAG, `不支持的位深度: ${format.bitsPerSample}，跳过交叉淡化`);
            result.set(segmentData, offset);
        }
    }

    /**
     * 16位音频的交叉淡化处理
     */
    private applyCrossfade16Bit(
        result: Uint8Array,
        segmentData: Uint8Array,
        offset: number,
        crossfadeSamples: number,
        format: AudioFormat
    ): void {
        for (let sample = 0; sample < crossfadeSamples; sample++) {
            const fadeRatio = sample / crossfadeSamples;

            for (let channel = 0; channel < format.channels; channel++) {
                const sampleOffset = (sample * format.channels + channel) * 2;
                const resultOffset = offset - crossfadeSamples * format.blockAlign + sampleOffset;

                if (resultOffset >= format.headerSize && resultOffset + 1 < result.length && sampleOffset + 1 < segmentData.length) {
                    // 读取16位有符号采样点（小端序）
                    const prevSample = (result[resultOffset + 1] << 8) | result[resultOffset];
                    const currSample = (segmentData[sampleOffset + 1] << 8) | segmentData[sampleOffset];

                    // 转换为有符号值
                    const prevSigned = prevSample > 32767 ? prevSample - 65536 : prevSample;
                    const currSigned = currSample > 32767 ? currSample - 65536 : currSample;

                    // 线性插值
                    const blended = Math.round(prevSigned * (1 - fadeRatio) + currSigned * fadeRatio);

                    // 转换回无符号值并写入
                    const blendedUnsigned = blended < 0 ? blended + 65536 : blended;
                    result[resultOffset] = blendedUnsigned & 0xff;
                    result[resultOffset + 1] = (blendedUnsigned >> 8) & 0xff;
                }
            }
        }
    }

    /**
     * 8位音频的交叉淡化处理
     */
    private applyCrossfade8Bit(result: Uint8Array, segmentData: Uint8Array, offset: number, crossfadeBytes: number): void {
        for (let i = 0; i < crossfadeBytes; i++) {
            const fadeRatio = i / crossfadeBytes;
            const resultOffset = offset - crossfadeBytes + i;

            if (resultOffset >= 0 && resultOffset < result.length && i < segmentData.length) {
                const prevSample = result[resultOffset];
                const currSample = segmentData[i];

                // 8位音频通常是无符号的，范围0-255
                const blended = Math.round(prevSample * (1 - fadeRatio) + currSample * fadeRatio);
                result[resultOffset] = Math.max(0, Math.min(255, blended));
            }
        }
    }

    /**
     * 分析音频波形数据
     * 参考Java版本的analyzeWaveform方法实现
     * @param audioData 音频字节数组（包含WAV头）
     * @returns number[] 波形数据数组
     */
    private analyzeWaveform(audioData: Uint8Array): number[] {
        // 跳过WAV文件头（前44字节），只分析音频数据部分
        const audioDataOnly = audioData.slice(44);

        // 计算总样本数（每2字节一个样本）
        const totalSamples = Math.floor(audioDataOnly.length / 2);

        // 使用浮点数计算采样间隔
        const samplesPerInterval = totalSamples / 120.0;

        const rawVolumes: number[] = new Array(110); // 存储原始音量值
        const smoothedVolumes: number[] = new Array(110); // 存储平滑后的音量值

        // 调整平滑因子α为0.5，增强音量特征保留能力
        const alpha = 0.5;

        for (let j = 0; j < 110; j++) {
            let maxVolume = 0; // 使用最大值代替平均值
            let sampleCount = 0;

            // 计算当前区间的开始和结束位置
            const start = Math.floor(j * samplesPerInterval);
            const end = Math.min(Math.floor((j + 1) * samplesPerInterval), totalSamples);

            for (let i = start; i < end; i++) {
                const byteIndex = i * 2;
                if (byteIndex + 1 < audioDataOnly.length) {
                    // 读取16位有符号音频样本（小端序）
                    const sample = (audioDataOnly[byteIndex + 1] << 8) | audioDataOnly[byteIndex];
                    // 转换为有符号16位整数
                    const signedSample = sample > 32767 ? sample - 65536 : sample;
                    // 计算绝对值并归一化到0-1范围
                    const volume = Math.abs(signedSample / 32767.0);
                    // 记录区间内最大音量值
                    if (volume > maxVolume) {
                        maxVolume = volume;
                    }
                    sampleCount++;
                }
            }

            // 使用区间最大音量值
            rawVolumes[j] = sampleCount > 0 ? maxVolume : 0;

            // 应用指数移动平均(EMA)算法
            if (j === 0) {
                smoothedVolumes[j] = rawVolumes[j];
            } else {
                // 增强当前采样点权重，保留更多原始特征
                smoothedVolumes[j] = alpha * rawVolumes[j] + (1 - alpha) * smoothedVolumes[j - 1];
            }
        }

        // 生成最终的波形数据数组
        const waveformData: number[] = [];
        for (let j = 0; j < 110; j++) {
            // 添加音量放大系数（1.5倍），增强特征表现
            const amplifiedVolume = smoothedVolumes[j] * 1.5;
            // 确保不超过1.0
            waveformData.push(Math.min(amplifiedVolume, 1.0));
        }

        console.log(TAG, `Waveform analysis completed, generated ${waveformData.length} data points`);
        return waveformData;
    }

    /**
     * 将base64字符串转换为Uint8Array
     * 替代浏览器的atob方法，适用于React Native环境
     * @param base64 base64编码的字符串
     * @returns Uint8Array 字节数组
     */
    private base64ToUint8Array(base64: string): Uint8Array {
        // 移除可能的数据URL前缀
        const cleanBase64 = base64.replace(/^data:.*,/, "");

        // Base64字符集
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        let result = "";

        // 解码base64
        for (let i = 0; i < cleanBase64.length; i += 4) {
            const a = chars.indexOf(cleanBase64[i]);
            const b = chars.indexOf(cleanBase64[i + 1]);
            const c = chars.indexOf(cleanBase64[i + 2]);
            const d = chars.indexOf(cleanBase64[i + 3]);

            const bitmap = (a << 18) | (b << 12) | (c << 6) | d;

            result += String.fromCharCode((bitmap >> 16) & 255);
            if (c !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
            if (d !== 64) result += String.fromCharCode(bitmap & 255);
        }

        // 转换为Uint8Array
        const bytes = new Uint8Array(result.length);
        for (let i = 0; i < result.length; i++) {
            bytes[i] = result.charCodeAt(i);
        }

        return bytes;
    }

    /**
     * 将Uint8Array转换为base64字符串
     * 替代浏览器的btoa方法，适用于React Native环境
     * @param bytes 字节数组
     * @returns string base64编码的字符串
     */
    private uint8ArrayToBase64(bytes: Uint8Array): string {
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        let result = "";

        for (let i = 0; i < bytes.length; i += 3) {
            const a = bytes[i];
            const b = i + 1 < bytes.length ? bytes[i + 1] : 0;
            const c = i + 2 < bytes.length ? bytes[i + 2] : 0;

            const bitmap = (a << 16) | (b << 8) | c;

            result += chars.charAt((bitmap >> 18) & 63);
            result += chars.charAt((bitmap >> 12) & 63);
            result += i + 1 < bytes.length ? chars.charAt((bitmap >> 6) & 63) : "=";
            result += i + 2 < bytes.length ? chars.charAt(bitmap & 63) : "=";
        }

        return result;
    }
}

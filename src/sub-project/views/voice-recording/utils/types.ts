/**
 * 语音录制组件相关类型定义
 * create date 2024/06/16
 */
import { Animation } from "@hippy/react";
import AbcAudioView from "../../../base-ui/views/abc-audio-view";
import { AsrResult } from "../../../AI/asr";
import { Patient } from "../../../base-business/data/beans";

export interface VoiceRecordingOverlayViewProps {
    onToggleFullscreen?: () => void;
    progress?: number; // 录制进度 0-100
    duration?: number; // 录制时长(秒)
    isRecording?: boolean; // 是否正在录制
    isMinimized?: boolean; // 是否最小化状态
    waveformData?: number[]; // 波形数据数组
    showRecord?: boolean; // 是否显示录制页面
}

export interface RecognitionResult {
    text: string;
    isFinal: boolean;
}

export interface VoiceRecordSegment {
    id: string;
    startTime: number; // 开始时间（秒）
    endTime: number; // 结束时间（秒）
    text: string; // 文本内容
    speaker?: string; // 说话人（可选）
}

export interface VoiceRecordingOverlayViewState {
    position: { top: number; left: number };
    isMinimized: boolean;
    progress: number;
    duration: number;
    isRecording: boolean;
    isPaused: boolean; // 是否暂停状态
    waveformData: number[]; // 波形数据
    toastMessage: string | null;
    toastVisible: boolean;
    // 新增弹窗相关状态
    dialogVisible: boolean;
    dialogTitle: string | null;
    dialogContent: string | null;
    dialogConfirmText: string | null;
    dialogCancelText: string | null;
    dialogOnConfirm: (() => void) | null;
    dialogOnCancel: (() => void) | null;
    recognitionResults: RecognitionResult[]; // 按sessionId存储结果
    activeSessionId: string | null; // 当前活跃的sessionId
    showRecord: boolean;
    showMedicalRecordView: boolean;
    // 动画相关状态
    fullscreenOpacity: number; // 全屏视图透明度
    minimizedOpacity: number; // 最小化视图透明度
    enableMedicalRecordTranscription: boolean; // 是否启用病历转写功能
    businessId: string;
    chineseExamination: number;
    physicalExamination: number;
    medicalRecordType: number;
}

export interface VoiceRecordingPageViewState {
    // 病历转写相关状态
    duration: number;
    recognitionResults: RecognitionResult[];
    currentTab: "voice" | "medical"; // 当前选中的tab
    medicalRecordData: string | null; // 病历数据
    isTranscribing: boolean; // 是否正在转写中
    // 语音记录相关状态
    voiceRecordResult: AsrResult;
    voiceRecordSegments: VoiceRecordSegment[]; // 语音记录片段
    currentPlayTime: number; // 当前播放时间 毫秒
    totalDuration: number; // 总时长 毫秒
    isPlaying: boolean; // 是否正在播放
    playbackSpeed: number; // 播放速度（1x, 1.5x, 2x等）
    highlightedSegmentId: string | null; // 当前高亮的片段ID
    // 语音识别相关状态
    businessId: string;
    taskId: string;
    waveformData: number[];
    patient: Patient;
    synthesizedFilePath: string;
    created: string;
}

export interface ASRConfig {
    serverUrl: string;
    language: string;
    sampleRate: number;
    channels: number;
    audioFormat: number;
    enableVad: boolean;
    enablePunctuation: boolean;
    enableNumberConvert: boolean;
    enableDirtyFilter: boolean;
    enableSilentDetection: boolean;
    silentTimeout: number;
    silentThreshold: number;
    enableForegroundService: boolean;
    serviceNotificationTitle: string;
    serviceNotificationContent: string;
    // 添加缺失的回调间隔配置
    volumeCallbackInterval: number;        // 音量回调间隔（毫秒）
    waveformCallbackInterval: number;      // 波形数据回调间隔（毫秒）
    extraHeaders: any;
    cookies: string;
    connectParams: any;
    forceWebsockets: boolean;
    namespace: string;
    path: string;
    reconnectionDelay: number;
}

export interface ASRStartResult {
    sessionId: string;
    code: number;
    message: string;
}

export interface AbcASTSocketOn {
    event: string;
    message: string;
}

export interface TouchPosition {
    top: number;
    right: number;
}

export interface RenderMethodsProps {
    state: VoiceRecordingOverlayViewState;
    touchPointPosition: TouchPosition;
    onToggleSize: () => void;
    onPauseRecord: () => void;
    onCloseRecord?: () => void;
    onStartRecord?: () => void;
    onStopRecord?: () => void;
    onTouchDown: (e: any) => void;
    onTouchEnd: (e: any) => void;
    onTouchMove: (e: any) => void;
    onTouchCancel: () => void;
    onDialogConfirm?: () => void;
    onDialogCancel?: () => void;
    setAudioViewRef?: (ref: AbcAudioView | null) => void; // 设置音频组件引用
    fullscreenOpacityAnimation?: Animation | null; // 全屏视图透明度动画
    minimizedOpacityAnimation?: Animation | null; // 最小化视图透明度动画
}

export interface RenderMedicalRecordProps {
    state: VoiceRecordingPageViewState;
    onBackToRecord?: () => void; // 返回录制页面
    onTabChange?: (tab: "voice" | "medical") => void; // tab切换
    fullscreenOpacityAnimation?: Animation | null; // 全屏视图透明度动画
    minimizedOpacityAnimation?: Animation | null; // 最小化视图透明度动画
    setVoiceViewRef?: (ref: AbcAudioView | null) => void; // 设置音频组件引用
    // 语音记录相关回调
    onPlayPause?: () => void; // 播放/暂停回调
    onSeekTo?: (time: number) => void; // 跳转到指定时间
    onSpeedChange?: (speed: number) => void; // 改变播放速度
    onSegmentClick?: (segmentId: string) => void; // 点击片段回调
    onScrollToSegment?: (segmentId: string) => void; // 滚动到指定片段回调
    onPlaybackSpeed?: () => void;
    onAcceptClick?: () => void;
    onHandleTimeUpdate?: (event: any) => void;
}

export interface ToastMessageDialogProps {
    title: string;
    content: string;
    icon: string;
}

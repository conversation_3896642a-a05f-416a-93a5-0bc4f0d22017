/**
 * 波形渲染方法
 * create date 2024/06/16
 */
import React from "react";
import { View } from "@hippy/react";
import { Colors, Sizes } from "../../../theme";
import { IconFontView, WaveVisualizationV2 } from "../../../base-ui";
import type { VoiceRecordingOverlayViewState } from "../utils/types";

/**
 * 渲染波形
 */
export const renderWaveform = (state: VoiceRecordingOverlayViewState, screenWidth: number): JSX.Element => {
    const { waveformData, isPaused } = state;
    if (!waveformData.length) return <View />;

    const width = Math.round(screenWidth);
    const height = Math.round(Sizes.dp100);
    const barWidth = Math.round(Sizes.dp1 + Sizes.dpHalf);
    const barGap = Math.round(Sizes.dp2);
    const barMinHeight = Math.round(Sizes.dp2);

    return (
        <WaveVisualizationV2
            waveformData={waveformData}
            isPaused={isPaused}
            waveColor={Colors.G2}
            pausedColor={Colors.G2}
            width={width}
            height={height}
            barWidth={barWidth}
            barMinHeight={barMinHeight}
            barGap={barGap}
        />
    );
};

/**
 * 渲染静态波形
 */
export const renderStaticWaveform = (): JSX.Element => {
    return (
        <IconFontView
            name={"s-track-line"}
            size={Sizes.dp32}
            color={Colors.T4}
            style={{
                marginBottom: Sizes.dp24,
            }}
        />
    );
};

/**
 * 线性插值函数
 * @param start 起始值
 * @param end 结束值
 * @param factor 插值因子 (0-1)
 * @returns 插值结果
 */
const linearInterpolate = (start: number, end: number, factor: number): number => {
    return start + (end - start) * factor;
};

/**
 * 生成插值波形数据，确保填满整个可用宽度
 * @param originalData 原始波形数据
 * @param targetLength 目标长度
 * @returns 插值后的波形数据
 */
const interpolateWaveformData = (originalData: number[], targetLength: number): number[] => {
    if (originalData.length === 0) {
        // 如果没有原始数据，生成低幅度的随机波形
        return Array.from({ length: targetLength }, () => Math.random() * 0.1 + 0.05);
    }

    if (originalData.length >= targetLength) {
        // 如果原始数据足够，直接截取
        return originalData.slice(0, targetLength);
    }

    const interpolatedData: number[] = [];
    const sourceLength = originalData.length;

    for (let i = 0; i < targetLength; i++) {
        // 计算在原始数据中的位置
        const sourcePosition = (i / (targetLength - 1)) * (sourceLength - 1);
        const sourceIndex = Math.floor(sourcePosition);
        const nextIndex = Math.min(sourceIndex + 1, sourceLength - 1);
        const factor = sourcePosition - sourceIndex;

        // 线性插值
        const interpolatedValue = linearInterpolate(
            originalData[sourceIndex],
            originalData[nextIndex],
            factor
        );

        // 添加轻微的随机变化以保持自然感
        const variation = (Math.random() - 0.5) * 0.02; // ±1% 的随机变化
        const finalValue = Math.max(0.01, Math.min(1, interpolatedValue + variation));

        interpolatedData.push(finalValue);
    }

    return interpolatedData;
};

/**
 * 渲染播放控制器中的波形（只显示上半部分）
 * @param screenWidth 屏幕宽度
 * @param waveformData 波形数据数组
 * @param playbackProgress 播放进度 (0-1)，可选参数，用于显示播放位置
 */
export const renderPlaybackWaveform = (
    screenWidth: number,
    waveformData: number[],
    playbackProgress?: number
): JSX.Element => {
    // 确保波形宽度与进度条宽度完全一致
    // 计算方式：屏幕宽度 - 父容器左右边距(16dp*2) - 容器左右padding(8dp*2)
    const waveformWidth = screenWidth - Sizes.dp16 * 2 - Sizes.dp8 * 2;
    const waveformHeight = Sizes.dp20;
    const barWidth = Sizes.dp1;
    const barGap = Sizes.dp2;
    const barSpacing = barWidth + barGap;

    // 计算可以显示的波形条数量，确保不超出容器宽度
    const maxBars = Math.floor(waveformWidth / barSpacing);

    // 使用插值确保波形数据填满整个可用宽度
    const displayData = interpolateWaveformData(waveformData, maxBars);

    // 计算当前播放位置对应的波形条索引
    const currentPlayIndex = playbackProgress !== undefined ?
        Math.floor(playbackProgress * displayData.length) : -1;

    return (
        <View
            style={{
                width: waveformWidth, // 明确设置容器宽度与进度条一致
                height: waveformHeight,
                flexDirection: "row",
                alignItems: "flex-end", // 从底部对齐，向上延伸
                justifyContent: "flex-start",
            }}
        >
            {displayData.map((amplitude, index) => {
                // 计算波形条的高度，从底部向上延伸
                const barHeight = Math.max(Sizes.dp2, amplitude * waveformHeight);

                // 根据播放进度确定波形条颜色
                // 已播放部分使用主色调，未播放部分使用灰色
                const isPlayed = playbackProgress !== undefined && index <= currentPlayIndex;
                const barColor = isPlayed ? Colors.B3 : Colors.P1;

                return (
                    <View
                        key={index}
                        style={{
                            width: barWidth,
                            height: barHeight,
                            backgroundColor: barColor,
                            borderRadius: barWidth / 2,
                            marginRight: index < displayData.length - 1 ? barGap : 0,
                        }}
                    />
                );
            })}
        </View>
    );
};

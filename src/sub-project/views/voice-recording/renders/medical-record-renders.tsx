/**
 * 病历转写视图渲染方法
 * create date 2024/06/16
 */
import React from "react";
import { Dimensions, Text, View, ScrollView } from "@hippy/react";
import { Colors, Sizes, TextStyles } from "../../../theme";
import type { RenderMedicalRecordProps, VoiceRecordSegment } from "../utils/types";
import { AiLoadingSpinner } from "../components/ai-loading-spinner";
import { Markdown } from "../../markdown-views";
import { IconFontView } from "../../../base-ui";
import { renderPlaybackWaveform } from "./waveform-renders";
import AbcAudioView from "../../../base-ui/views/abc-audio-view";
import { VoiceRecordingPageViewState } from "../utils/types";
import WillPopListener from "../../../base-ui/views/will-pop-listener";
import { OutpatientUtils } from "../../../outpatient/utils/outpatient-utils";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";

/**
 * 渲染病历转写视图
 */
export const renderMedicalRecordView = (props: RenderMedicalRecordProps): JSX.Element => {
    const { state, onBackToRecord, onAcceptClick } = props;
    const { currentTab, medicalRecordData } = state;
    const screenWidth = Dimensions.get("window").width;
    const screenHeight = Dimensions.get("window").height;
    return (
        <View
            style={{
                flex: 1,
                backgroundColor: Colors.white,
                paddingTop: Sizes.dp24,
                width: screenWidth,
            }}
        >
            <WillPopListener
                onWillPop={() => {
                    OutpatientUtils.voiceRecordingAsrResult.next(true);
                    ABCNavigator.pop();
                }}
            />
            {/* 内容区域 */}
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {currentTab === "medical"
                    ? renderMedicalRecordContent(medicalRecordData, screenHeight, screenWidth, state, onBackToRecord, onAcceptClick)
                    : renderVoiceRecordContent(state, props)}
            </View>
        </View>
    );
};

/**
 * 渲染病历内容（可复用）
 */
export const renderMedicalRecordContent = (
    medicalRecordData: string | null,
    screenHeight: number,
    screenWidth: number,
    state: VoiceRecordingPageViewState,
    onBackToRecord?: () => void,
    onAcceptClick?: () => void
): JSX.Element => {
    const { isTranscribing, patient } = state;
    return (
        <View style={{ paddingHorizontal: Sizes.dp16 }}>
            {/* 转写状态提示 */}
            <View
                style={{
                    marginBottom: Sizes.dp16,
                    flexGrow: 0,
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "row",
                }}
            >
                <View
                    style={{
                        borderRadius: Sizes.dp50,
                        paddingHorizontal: Sizes.dp8,
                        paddingVertical: Sizes.dp0,
                        backgroundColor: "#F2F4F7",
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "row",
                    }}
                >
                    <AiLoadingSpinner loading={isTranscribing || false} />
                    <Text
                        style={[
                            TextStyles.t13NM,
                            {
                                color: "#1890FF",
                                marginLeft: Sizes.dp1,
                            },
                        ]}
                    >
                        {isTranscribing ? "正在将语音转写成病历…" : "病历转写成功"}
                    </Text>
                </View>
            </View>

            {patient && (
                <View
                    style={{
                        height: Sizes.dp56,
                        borderBottomWidth: Sizes.dp1,
                        borderColor: Colors.dividerLineColor,
                        borderStyle: "dashed",
                    }}
                >
                    <View>
                        <Text
                            style={[
                                TextStyles.t14NT3,
                                {
                                    color: Colors.T3,
                                    marginBottom: Sizes.dp8,
                                },
                            ]}
                        >
                            患者：{patient?.name ?? ""} {patient?.sex ?? ""} {patient?.age?.displayAgeIncludeMonth ?? ""}
                        </Text>
                    </View>
                    <View>
                        <Text
                            style={[
                                TextStyles.t14NT3,
                                {
                                    color: Colors.T3,
                                    marginBottom: Sizes.dp8,
                                },
                            ]}
                        >
                            时间：{new Date(state.created).format("yyyy-MM-dd HH:mm")}
                        </Text>
                    </View>
                </View>
            )}

            <View style={{ flex: 1, height: screenHeight - Sizes.dp180 - Sizes.dp36 }}>
                <ScrollView style={{ flex: 1 }}>
                    <Markdown key={"markdown-" + medicalRecordData?.length}>{medicalRecordData || " "}</Markdown>
                </ScrollView>

                {/* 底部按钮 */}
                {isTranscribing ? (
                    <View></View>
                ) : (
                    <View
                        style={{
                            flexDirection: "row",
                            paddingVertical: Sizes.dp16,
                            paddingBottom: Sizes.dp12,
                        }}
                    >
                        {/* 重新录制按钮 */}
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: Colors.white,
                                borderRadius: Sizes.dp28,
                                paddingVertical: Sizes.dp14,
                                marginRight: Sizes.dp8,
                                justifyContent: "center",
                                alignItems: "center",
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.P7,
                                height: Sizes.dp56,
                            }}
                            onClick={onBackToRecord}
                        >
                            <Text
                                style={[
                                    TextStyles.t16NM,
                                    {
                                        color: "#F74572",
                                    },
                                ]}
                            >
                                重新录制
                            </Text>
                        </View>

                        {/* 采纳按钮 */}
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: "#1890FF",
                                borderRadius: Sizes.dp28,
                                paddingVertical: Sizes.dp14,
                                marginLeft: Sizes.dp8,
                                justifyContent: "center",
                                alignItems: "center",
                                height: Sizes.dp56,
                                backgroundImage: "linear-gradient(101deg, #5ACAFD 0%, #5694FE 58.75%, #9055FE 100%)",
                            }}
                            onClick={onAcceptClick}
                        >
                            <Text
                                style={[
                                    TextStyles.t16NM,
                                    {
                                        color: Colors.white,
                                    },
                                ]}
                            >
                                采纳
                            </Text>
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

/**
 * 渲染病历章节
 */
export const renderMedicalRecordSection = (title: string, content: string): JSX.Element => {
    return (
        <View style={{ marginBottom: Sizes.dp24 }}>
            <Text
                style={[
                    TextStyles.t16NM,
                    {
                        color: Colors.T2,
                        marginBottom: Sizes.dp8,
                    },
                ]}
            >
                {title}
            </Text>
            <Text
                style={[
                    TextStyles.t16NM,
                    {
                        color: Colors.T1,
                        lineHeight: Sizes.dp20,
                    },
                ]}
            >
                {content}
            </Text>
        </View>
    );
};

/**
 * 根据当前播放时间找到对应的片段ID
 */
const findSegmentByTime = (segments: VoiceRecordSegment[], currentTime: number): string | null => {
    // 修复查找逻辑：使用 < 而不是 <= 来避免边界重叠问题
    const segment = segments.find((s) => currentTime >= s.startTime && currentTime < s.endTime);

    // 如果没找到，尝试找最接近的片段（容错处理）
    if (!segment) {
        const closestSegment = segments.find((s) => Math.abs(currentTime - s.startTime) < 0.5);
        return closestSegment ? closestSegment.id : null;
    }

    return segment.id;
};

// 存储语音片段的位置信息和滚动引用
const segmentPositions = new Map<string, number>();
let scrollViewRef: ScrollView | null = null;
let lastHighlightedSegmentId: string | null = null;

/**
 * 自动滚动到指定片段
 */
const scrollToSegment = (segmentId: string) => {
    if (segmentId && scrollViewRef) {
        const segmentPosition = segmentPositions.get(segmentId);
        if (segmentPosition !== undefined) {
            // 滚动到片段位置，留一些边距
            const scrollOffset = Math.max(0, segmentPosition - Sizes.dp100);
            scrollViewRef.scrollTo(
                {
                    x: 0,
                    y: scrollOffset,
                    animated: true,
                },
                scrollOffset,
                true
            );
            console.log(`Auto scroll to segment ${segmentId} at position ${segmentPosition}`);
        }
    }
};

/**
 * 渲染语音记录内容
 */
const renderVoiceRecordContent = (state: VoiceRecordingPageViewState, props: RenderMedicalRecordProps): JSX.Element => {
    const { voiceRecordSegments, currentPlayTime, totalDuration, isPlaying, playbackSpeed, highlightedSegmentId, waveformData } = state;

    const { onHandleTimeUpdate, setVoiceViewRef, onPlayPause, onSeekTo, onSpeedChange, onSegmentClick } = props;

    // 优先使用手动设置的高亮片段，如果没有则根据播放时间自动高亮
    const effectiveHighlightedSegmentId =
        highlightedSegmentId || (isPlaying ? findSegmentByTime(voiceRecordSegments || [], currentPlayTime) : null);

    // 检查高亮片段是否发生变化，如果变化则自动滚动
    if (effectiveHighlightedSegmentId !== lastHighlightedSegmentId) {
        lastHighlightedSegmentId = effectiveHighlightedSegmentId;
        if (effectiveHighlightedSegmentId) {
            // 使用 setTimeout 来确保在下一个事件循环中执行滚动
            setTimeout(() => {
                scrollToSegment(effectiveHighlightedSegmentId);
            }, 100);
        }
    }

    // 如果没有语音记录数据，显示空状态
    if (!voiceRecordSegments || voiceRecordSegments.length === 0) {
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp24,
                }}
            >
                <Text
                    style={[
                        TextStyles.t16NT3,
                        {
                            textAlign: "center",
                            color: Colors.T3,
                        },
                    ]}
                >
                    暂无语音记录
                </Text>
            </View>
        );
    }

    return (
        <View style={{ flex: 1, paddingHorizontal: Sizes.dp16 }}>
            {/* 语音记录列表 */}
            <ScrollView
                ref={(ref) => {
                    scrollViewRef = ref;
                }}
                style={{
                    flex: 1,
                    marginBottom: Sizes.dp148,
                }}
                showsVerticalScrollIndicator={false}
            >
                {voiceRecordSegments.map((segment, index) =>
                    renderVoiceSegment(segment, index, effectiveHighlightedSegmentId, onSegmentClick, onSeekTo, segmentPositions)
                )}
            </ScrollView>

            {/* 底部播放控制器 */}
            {renderPlaybackController(
                currentPlayTime,
                totalDuration,
                isPlaying,
                playbackSpeed,
                waveformData,
                setVoiceViewRef,
                onPlayPause,
                onSeekTo,
                onSpeedChange,
                onHandleTimeUpdate
            )}
        </View>
    );
};

/**
 * 渲染语音片段
 */
const renderVoiceSegment = (
    segment: VoiceRecordSegment,
    index: number,
    highlightedSegmentId: string | null,
    onSegmentClick?: (segmentId: string) => void,
    onSeekTo?: (time: number) => void,
    segmentPositions?: Map<string, number>
): JSX.Element => {
    const isHighlighted = segment.id === highlightedSegmentId;
    const formatTime = (millis: number): string => {
        const seconds = millis / 1000;
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `[${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}]`;
    };

    return (
        <View
            key={segment.id}
            style={{
                flexDirection: "row",
                paddingVertical: Sizes.dp12,
                paddingHorizontal: Sizes.dp12,
                marginVertical: Sizes.dp4,
                backgroundColor: isHighlighted ? "#f2f4f7" : Colors.white,
                borderRadius: Sizes.dp16,
                overflow: "hidden",
                transform: isHighlighted ? [{ scale: 1.02 }] : [{ scale: 1 }],
            }}
            onClick={() => {
                onSegmentClick?.(segment.id);
                // 同时跳转到该片段的开始时间
                onSeekTo?.(segment.startTime);
            }}
            onLayout={(event) => {
                // 记录每个片段的位置
                if (segmentPositions) {
                    const { y } = event.layout;
                    segmentPositions.set(segment.id, y);
                    console.log(`Segment ${segment.id} layout at y: ${y}`);
                }
            }}
        >
            {/* 头像 */}
            <View
                style={{
                    width: Sizes.dp22,
                    height: Sizes.dp22,
                    borderRadius: Sizes.dp22,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: Sizes.dp12,
                }}
            >
                <IconFontView name="s-avatar-fill" size={Sizes.dp22} color={Colors.T3} />
            </View>

            {/* 内容区域 */}
            <View style={{ flex: 1 }}>
                {/* 时间标签 */}
                <Text
                    style={[
                        TextStyles.t12NT4,
                        {
                            marginBottom: Sizes.dp4,
                            fontWeight: isHighlighted ? "600" : "normal",
                        },
                    ]}
                >
                    {formatTime(segment.startTime)}
                </Text>

                {/* 说话人标签 */}
                {/*{segment.speaker && (*/}
                {/*    <Text*/}
                {/*        style={[*/}
                {/*            TextStyles.t11NT2,*/}
                {/*            {*/}
                {/*                color: isHighlighted ? "#1976D2" : Colors.T3,*/}
                {/*                marginBottom: Sizes.dp2,*/}
                {/*                fontSize: Sizes.dp10,*/}
                {/*                fontWeight: isHighlighted ? "600" : "normal",*/}
                {/*            },*/}
                {/*        ]}*/}
                {/*    >*/}
                {/*        {segment.speaker}*/}
                {/*    </Text>*/}
                {/*)}*/}

                {/* 文本内容 */}
                <Text
                    style={[
                        TextStyles.t14NM,
                        {
                            color: isHighlighted ? Colors.T1 : Colors.T2,
                            lineHeight: Sizes.dp20,
                            fontWeight: isHighlighted ? "500" : "normal",
                        },
                    ]}
                >
                    {segment.text || "-"}
                </Text>
            </View>
        </View>
    );
};

// 全局变量来跟踪拖动状态（Hippy 兼容方式）
let isDragging = false;
let dragProgress = 0;
let dragStartTime = 0;

/**
 * 渲染播放控制器
 */
const renderPlaybackController = (
    currentTime: number,
    totalDuration: number,
    isPlaying: boolean,
    playbackSpeed: number,
    waveformData: number[],
    setVoiceViewRef?: (ref: AbcAudioView | null) => void,
    onPlayPause?: () => void,
    onSeekTo?: (time: number) => void,
    onSpeedChange?: (speed: number) => void,
    onHandleTimeUpdate?: (event: any) => void
): JSX.Element => {
    const formatTime = (millis: number): string => {
        // 转换毫秒
        const seconds = millis / 1000;
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    // 计算实际显示的进度：拖动时使用拖动进度，否则使用当前播放进度，单位毫秒
    const actualProgress = isDragging ? dragProgress : totalDuration > 0 ? currentTime / totalDuration : 0;
    const speedOptions = [1, 1.5, 2];

    // 调试信息
    console.log("Progress bar render:", { currentTime, totalDuration, actualProgress, isDragging, dragProgress });
    return (
        <View
            style={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: Colors.white,
                paddingHorizontal: Sizes.dp16,
                paddingTop: Sizes.dp8,
                paddingBottom: Sizes.dp16,
                borderTopWidth: Sizes.dp1,
                borderColor: Colors.dividerLineColor,
            }}
        >
            <View>
                <AbcAudioView ref={setVoiceViewRef} onTimeupdate={onHandleTimeUpdate} src="" />
            </View>
            {/* 波形图 - 只显示上半部分 */}
            <View
                style={{
                    marginBottom: -Sizes.dp8,
                    paddingHorizontal: Sizes.dp8,
                }}
            >
                {renderPlaybackWaveform(Dimensions.get("window").width, waveformData, actualProgress)}
            </View>

            {/* 进度条 */}
            <View
                style={{
                    marginBottom: Sizes.dp12,
                }}
            >
                {/* 进度条容器 */}
                <View
                    style={{
                        height: Sizes.dp20, // 增加触摸区域
                        justifyContent: "center",
                        paddingHorizontal: Sizes.dp8,
                    }}
                    onTouchDown={(e) => {
                        // 开始拖动
                        isDragging = true;
                        dragStartTime = Date.now();

                        // 计算触摸位置对应的进度
                        const screenWidth = Dimensions.get("window").width;
                        const progressBarStartX = Sizes.dp16 + Sizes.dp8; // 左边距 + padding
                        const progressBarEndX = screenWidth - Sizes.dp16 - Sizes.dp8; // 右边距 + padding
                        const progressBarWidth = progressBarEndX - progressBarStartX;

                        const touchX = e.page_x - progressBarStartX;
                        const clickProgress = Math.max(0, Math.min(1, touchX / progressBarWidth));

                        // 立即更新拖动进度，让滑块跟随
                        dragProgress = clickProgress;

                        console.log("Progress bar touch down:", {
                            page_x: e.page_x,
                            screenWidth,
                            progressBarStartX,
                            progressBarWidth,
                            touchX,
                            clickProgress,
                        });
                    }}
                    onTouchMove={(e) => {
                        if (!isDragging) return;

                        // 计算拖动位置对应的进度
                        const screenWidth = Dimensions.get("window").width;
                        const progressBarStartX = Sizes.dp16 + Sizes.dp8;
                        const progressBarEndX = screenWidth - Sizes.dp16 - Sizes.dp8;
                        const progressBarWidth = progressBarEndX - progressBarStartX;

                        const touchX = e.page_x - progressBarStartX;
                        const newDragProgress = Math.max(0, Math.min(1, touchX / progressBarWidth));
                        // 实时更新拖动进度，让滑块跟随手指
                        dragProgress = newDragProgress;
                        // 调用 onSeekTo 更新实际播放位置
                        const targetTime = dragProgress * totalDuration;
                        onSeekTo?.(targetTime);
                        console.log("Progress bar drag move:", {
                            page_x: e.page_x,
                            touchX,
                            newDragProgress,
                        });
                    }}
                    onTouchEnd={(e) => {
                        if (!isDragging) return;

                        // 结束拖动，应用最终位置
                        const targetTime = dragProgress * totalDuration;
                        isDragging = false;

                        // 调用 onSeekTo 更新实际播放位置
                        if (!isPlaying) {
                            onPlayPause?.();
                        }
                        onSeekTo?.(targetTime);

                        console.log("Progress bar touch end:", {
                            dragProgress,
                            targetTime,
                        });
                    }}
                    onTouchCancel={() => {
                        // 取消拖动
                        isDragging = false;
                        console.log("Progress bar touch cancel");
                    }}
                >
                    {/* 进度条轨道容器 - 增加高度以容纳滑块 */}
                    <View
                        style={{
                            height: Sizes.dp20, // 增加高度以容纳滑块
                            justifyContent: "center", // 垂直居中
                            position: "relative",
                        }}
                    >
                        {/* 进度条背景轨道 */}
                        <View
                            style={{
                                height: Sizes.dp4,
                                backgroundColor: "#E5E5E5",
                                borderRadius: Sizes.dp2,
                            }}
                        />

                        {/* 已播放进度 */}
                        <View
                            style={(() => {
                                const screenWidth = Dimensions.get("window").width;
                                const progressBarWidth = screenWidth - Sizes.dp16 * 2 - Sizes.dp8 * 2; // 减去左右边距和padding
                                const progressWidth = Math.max(0, actualProgress * progressBarWidth);

                                return {
                                    position: "absolute",
                                    top: Sizes.dp8, // 与背景轨道对齐
                                    height: Sizes.dp4,
                                    backgroundColor: Colors.B3,
                                    borderRadius: Sizes.dp2,
                                    width: progressWidth,
                                };
                            })()}
                        />

                        {/* 进度条滑块 */}
                        <View
                            style={(() => {
                                const screenWidth = Dimensions.get("window").width;
                                const progressBarWidth = screenWidth - Sizes.dp16 * 2 - Sizes.dp8 * 2; // 减去左右边距和padding
                                const sliderPosition = Math.max(
                                    0,
                                    Math.min(progressBarWidth - Sizes.dp16, actualProgress * progressBarWidth)
                                );

                                return {
                                    position: "absolute",
                                    left: sliderPosition,
                                    top: Sizes.dp2, // 滑块顶部位置，确保在容器内
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    backgroundColor: Colors.B3,
                                    borderRadius: Sizes.dp8,
                                    borderWidth: Sizes.dp2,
                                    borderColor: Colors.white,
                                };
                            })()}
                        />
                    </View>
                </View>

                {/* 时间显示 */}
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        paddingHorizontal: Sizes.dp8,
                    }}
                >
                    <Text
                        style={[
                            TextStyles.t12NT4,
                            {
                                color: Colors.T4,
                                minWidth: Sizes.dp40,
                            },
                        ]}
                    >
                        {formatTime(currentTime)}
                    </Text>
                    <Text
                        style={[
                            TextStyles.t12NT4,
                            {
                                color: Colors.T4,
                                minWidth: Sizes.dp40,
                            },
                        ]}
                    >
                        {formatTime(totalDuration)}
                    </Text>
                </View>
            </View>

            {/* 控制按钮 */}
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp4,
                }}
            >
                {/* 播放速度按钮 */}
                <View
                    style={{
                        paddingHorizontal: Sizes.dp8,
                        borderRadius: Sizes.dp16,
                        marginRight: Sizes.dp16,
                        fontWeight: "600",
                    }}
                    onClick={() => {
                        const currentIndex = speedOptions.indexOf(playbackSpeed);
                        const nextIndex = (currentIndex + 1) % speedOptions.length;
                        onSpeedChange?.(speedOptions[nextIndex]);
                    }}
                >
                    <Text
                        style={[
                            TextStyles.t12NM,
                            {
                                color: Colors.T2,
                            },
                        ]}
                    >
                        {playbackSpeed}x
                    </Text>
                </View>

                {/* 播放/暂停按钮 */}
                <View
                    style={{
                        width: Sizes.dp56,
                        height: Sizes.dp56,
                        borderRadius: Sizes.dp28,
                        backgroundColor: Colors.B3,
                        justifyContent: "center",
                        alignItems: "center",
                        position: "relative",
                        left: -Sizes.dp28,
                    }}
                    onClick={onPlayPause}
                >
                    {isPlaying ? (
                        <IconFontView key={"s-pause-fill"} name={"s-pause-fill"} size={Sizes.dp20} color={Colors.white} />
                    ) : (
                        <IconFontView key={"s-play-fill"} name={"s-play-fill"} size={Sizes.dp20} color={Colors.white} />
                    )}
                </View>

                <View
                    style={[
                        TextStyles.t12NM,
                        {
                            color: Colors.T2,
                        },
                    ]}
                ></View>
            </View>
        </View>
    );
};

import VoiceRecordingOverlayView from "../views/voice-recording/pages/voice-recording-page";
import { AsrResult } from "./asr";
import { Patient } from "../base-business/data/beans";
import { OutpatientInvoiceDetail } from "../outpatient/data/outpatient-beans";
import { VoiceRecordingOverlayManager } from "../views/voice-recording/manage/voice-recording-overlay-manager";

export class AIMedicalRecordsManager {
    static runVoiceRecordService(): void {
        console.log("AIMedicalRecordsManager", "[InitDebug] runVoiceRecordService called");
        // 启动VoiceRecordingOverlayManager，只初始化非页面相关的逻辑
        VoiceRecordingOverlayManager.getInstance().run(VoiceRecordingOverlayView);
        console.log("AIMedicalRecordsManager", "[InitDebug] VoiceRecordingOverlayManager.run completed");
    }

    static stopVoiceRecordService(): void {
        VoiceRecordingOverlayManager.getInstance().stop();
    }

    static async showMedicalRecordTranscriptionOverlay(
        patient?: Patient,
        businessId?: string,
        voiceRecordResult?: AsrResult,
        chineseExamination?: number,
        physicalExamination?: number,
        medicalRecordType?: number,
        draft?: OutpatientInvoiceDetail
    ): Promise<void> {
        console.log("AIMedicalRecordsManager", "[ShowDebug] showMedicalRecordTranscriptionOverlay called with:", {
            hasPatient: !!patient,
            businessId,
            hasVoiceRecordResult: !!voiceRecordResult,
            chineseExamination,
            physicalExamination,
            medicalRecordType,
            hasDraft: !!draft,
        });

        const result = await VoiceRecordingOverlayManager.getInstance().show(
            false,
            true,
            voiceRecordResult,
            businessId,
            patient,
            chineseExamination,
            physicalExamination,
            medicalRecordType,
            draft
        );

        console.log("AIMedicalRecordsManager", "[ShowDebug] show method returned:", result);
    }
}
